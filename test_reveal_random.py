#!/usr/bin/env python3
"""
专门测试reveal和random过渡效果
"""

from tool import PowerPointEditor

def test_reveal_random_transitions():
    """测试reveal和random过渡效果"""
    print("=== 测试reveal和random过渡效果 ===")
    
    # 创建编辑器实例
    editor = PowerPointEditor()
    
    # 1. 创建新演示文稿
    print("1. 创建新演示文稿...")
    result = editor.create_presentation()
    print(f"结果: {result}")
    
    # 2. 添加标题幻灯片
    print("\n2. 添加标题幻灯片...")
    result = editor.add_title_slide("Reveal和Random效果测试", "测试新增的过渡效果")
    print(f"结果: {result}")
    
    # 3. 测试reveal效果
    print("\n3. 测试reveal过渡效果...")
    
    # 添加幻灯片
    result = editor.add_slide(1)
    if result.get("success"):
        slide_index = result["slide_index"]
        
        # 添加内容
        result = editor.add_text_box(
            slide_index=slide_index,
            text="Reveal过渡效果",
            left=1,
            top=1,
            width=8,
            height=1,
            font_size=32
        )
        print(f"  添加标题: {result}")
        
        result = editor.add_text_box(
            slide_index=slide_index,
            text="这张幻灯片使用了reveal（揭开）过渡效果\n\n观察幻灯片是如何被揭开显示的",
            left=1,
            top=3,
            width=8,
            height=3,
            font_size=18
        )
        print(f"  添加说明: {result}")
        
        # 设置reveal过渡效果
        result = editor.set_slide_transition(
            slide_index=slide_index,
            transition_type="reveal",
            duration=1.5,
            advance_on_click=True
        )
        print(f"  设置reveal过渡: {result}")
    
    # 4. 测试uncover效果
    print("\n4. 测试uncover过渡效果...")
    
    result = editor.add_slide(1)
    if result.get("success"):
        slide_index = result["slide_index"]
        
        result = editor.add_text_box(
            slide_index=slide_index,
            text="Uncover过渡效果",
            left=1,
            top=1,
            width=8,
            height=1,
            font_size=32
        )
        
        result = editor.add_text_box(
            slide_index=slide_index,
            text="这张幻灯片使用了uncover（揭开覆盖）过渡效果\n\n类似于reveal，但实现方式略有不同",
            left=1,
            top=3,
            width=8,
            height=3,
            font_size=18
        )
        
        result = editor.set_slide_transition(
            slide_index=slide_index,
            transition_type="uncover",
            duration=1.5,
            advance_on_click=True
        )
        print(f"  设置uncover过渡: {result}")
    
    # 5. 测试random效果
    print("\n5. 测试random过渡效果...")
    
    result = editor.add_slide(1)
    if result.get("success"):
        slide_index = result["slide_index"]
        
        result = editor.add_text_box(
            slide_index=slide_index,
            text="Random过渡效果",
            left=1,
            top=1,
            width=8,
            height=1,
            font_size=32
        )
        
        result = editor.add_text_box(
            slide_index=slide_index,
            text="这张幻灯片使用了random（随机）过渡效果\n\n每次播放时会随机选择一种过渡动画",
            left=1,
            top=3,
            width=8,
            height=3,
            font_size=18
        )
        
        result = editor.set_slide_transition(
            slide_index=slide_index,
            transition_type="random",
            duration=1.0,
            advance_on_click=True
        )
        print(f"  设置random过渡: {result}")
    
    # 6. 添加对比幻灯片（使用fade）
    print("\n6. 添加对比幻灯片（fade效果）...")
    
    result = editor.add_slide(1)
    if result.get("success"):
        slide_index = result["slide_index"]
        
        result = editor.add_text_box(
            slide_index=slide_index,
            text="对比：Fade过渡效果",
            left=1,
            top=1,
            width=8,
            height=1,
            font_size=32
        )
        
        result = editor.add_text_box(
            slide_index=slide_index,
            text="这张幻灯片使用了标准的fade（淡入淡出）过渡效果\n\n用于对比新增的过渡效果",
            left=1,
            top=3,
            width=8,
            height=3,
            font_size=18
        )
        
        result = editor.set_slide_transition(
            slide_index=slide_index,
            transition_type="fade",
            duration=1.5,
            advance_on_click=True
        )
        print(f"  设置fade过渡: {result}")
    
    # 7. 获取更新后的过渡效果列表
    print("\n7. 获取更新后的过渡效果列表...")
    result = editor.get_available_transitions()
    print(f"结果: {result}")
    
    if result.get("success"):
        transitions = result.get("transitions", [])
        print(f"\n现在支持 {len(transitions)} 种过渡效果:")
        for i, transition in enumerate(transitions, 1):
            print(f"  {i}. {transition['name']} - {transition['description']}")
    
    # 8. 保存测试文件
    print("\n8. 保存测试文件...")
    result = editor.save_presentation("reveal_random_test.pptx")
    print(f"结果: {result}")
    
    # 9. 获取演示文稿信息
    print("\n9. 获取演示文稿信息...")
    result = editor.get_presentation_info()
    print(f"结果: {result}")

def main():
    """主测试函数"""
    try:
        test_reveal_random_transitions()
        
        print("\n=== reveal和random过渡效果测试完成 ===")
        print("生成的文件:")
        print("- reveal_random_test.pptx")
        print("\n使用说明:")
        print("1. 用PowerPoint打开 reveal_random_test.pptx")
        print("2. 按F5开始幻灯片放映")
        print("3. 仔细观察reveal、uncover和random过渡效果")
        print("4. 与fade效果进行对比")
        print("5. 如果某些效果不工作，可能需要调整XML格式")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
