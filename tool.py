#!/usr/bin/env python3
"""
PowerPoint编辑MCP Server
提供基础的PPT编辑功能，包括添加文本、图片、形状等
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# 导入PowerPoint相关库
try:
    from pptx import Presentation
    from pptx.util import Inches, Pt
    from pptx.enum.text import PP_ALIGN
    from pptx.enum.shapes import MSO_SHAPE
    from pptx.dml.color import RGBColor
    from pptx.enum.dml import MSO_THEME_COLOR
except ImportError:
    raise ImportError("请安装python-pptx库: pip install python-pptx")

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PowerPointEditor:
    """PowerPoint编辑器类"""

    def __init__(self):
        self.current_presentation: Optional[Presentation] = None
        self.current_file_path: Optional[str] = None

    def create_presentation(self) -> Dict[str, Any]:
        """创建新的演示文稿"""
        try:
            self.current_presentation = Presentation()
            self.current_file_path = None
            return {
                "success": True,
                "message": "成功创建新的演示文稿",
                "slides_count": len(self.current_presentation.slides)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def open_presentation(self, file_path: str) -> Dict[str, Any]:
        """打开现有的演示文稿"""
        try:
            if not Path(file_path).exists():
                return {"success": False, "error": f"文件不存在: {file_path}"}

            self.current_presentation = Presentation(file_path)
            self.current_file_path = file_path

            return {
                "success": True,
                "message": f"成功打开演示文稿: {file_path}",
                "slides_count": len(self.current_presentation.slides),
                "file_path": file_path
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def save_presentation(self, file_path: Optional[str] = None) -> Dict[str, Any]:
        """保存演示文稿"""
        try:
            if not self.current_presentation:
                return {"success": False, "error": "没有打开的演示文稿"}

            save_path = file_path or self.current_file_path
            if not save_path:
                return {"success": False, "error": "请指定保存路径"}

            self.current_presentation.save(save_path)
            self.current_file_path = save_path

            return {
                "success": True,
                "message": f"成功保存演示文稿: {save_path}",
                "file_path": save_path
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_slide(self, layout_index: int = 1) -> Dict[str, Any]:
        """添加新幻灯片"""
        try:
            if not self.current_presentation:
                return {"success": False, "error": "没有打开的演示文稿"}

            # 获取幻灯片布局
            slide_layouts = self.current_presentation.slide_layouts
            if layout_index >= len(slide_layouts):
                layout_index = 1  # 默认使用标题和内容布局

            layout = slide_layouts[layout_index]
            slide = self.current_presentation.slides.add_slide(layout)

            return {
                "success": True,
                "message": f"成功添加新幻灯片",
                "slide_index": len(self.current_presentation.slides) - 1,
                "total_slides": len(self.current_presentation.slides)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_text_box(self, slide_index: int, text: str, left: float = 1,
                     top: float = 1, width: float = 8, height: float = 1,
                     font_size: int = 18, font_color: str = "000000") -> Dict[str, Any]:
        """在指定幻灯片添加文本框"""
        try:
            if not self.current_presentation:
                return {"success": False, "error": "没有打开的演示文稿"}

            slides = self.current_presentation.slides
            if slide_index >= len(slides):
                return {"success": False, "error": f"幻灯片索引超出范围: {slide_index}"}

            slide = slides[slide_index]

            # 添加文本框
            left_inches = Inches(left)
            top_inches = Inches(top)
            width_inches = Inches(width)
            height_inches = Inches(height)

            textbox = slide.shapes.add_textbox(left_inches, top_inches, width_inches, height_inches)
            text_frame = textbox.text_frame
            text_frame.text = text

            # 设置字体样式
            paragraph = text_frame.paragraphs[0]
            font = paragraph.font
            font.size = Pt(font_size)

            # 设置字体颜色
            try:
                rgb_color = RGBColor.from_string(font_color)
                font.color.rgb = rgb_color
            except:
                pass  # 如果颜色格式不正确，使用默认颜色

            return {
                "success": True,
                "message": f"成功在幻灯片 {slide_index} 添加文本框",
                "text": text,
                "position": {"left": left, "top": top, "width": width, "height": height}
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_title_slide(self, title: str, subtitle: str = "") -> Dict[str, Any]:
        """添加标题幻灯片"""
        try:
            if not self.current_presentation:
                return {"success": False, "error": "没有打开的演示文稿"}

            # 使用标题幻灯片布局
            title_slide_layout = self.current_presentation.slide_layouts[0]
            slide = self.current_presentation.slides.add_slide(title_slide_layout)

            # 设置标题
            title_shape = slide.shapes.title
            title_shape.text = title

            # 设置副标题
            if subtitle and len(slide.placeholders) > 1:
                subtitle_shape = slide.placeholders[1]
                subtitle_shape.text = subtitle

            return {
                "success": True,
                "message": "成功添加标题幻灯片",
                "slide_index": len(self.current_presentation.slides) - 1,
                "title": title,
                "subtitle": subtitle
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_bullet_points(self, slide_index: int, title: str, bullet_points: List[str]) -> Dict[str, Any]:
        """添加带项目符号的内容幻灯片"""
        try:
            if not self.current_presentation:
                return {"success": False, "error": "没有打开的演示文稿"}

            slides = self.current_presentation.slides
            if slide_index >= len(slides):
                return {"success": False, "error": f"幻灯片索引超出范围: {slide_index}"}

            slide = slides[slide_index]

            # 设置标题
            if slide.shapes.title:
                slide.shapes.title.text = title

            # 查找内容占位符
            content_placeholder = None
            for shape in slide.placeholders:
                if shape.placeholder_format.idx == 1:  # 内容占位符通常是索引1
                    content_placeholder = shape
                    break

            if content_placeholder:
                text_frame = content_placeholder.text_frame
                text_frame.clear()  # 清除现有内容

                for i, point in enumerate(bullet_points):
                    if i == 0:
                        p = text_frame.paragraphs[0]
                    else:
                        p = text_frame.add_paragraph()
                    p.text = point
                    p.level = 0  # 设置为第一级项目符号

            return {
                "success": True,
                "message": f"成功在幻灯片 {slide_index} 添加项目符号内容",
                "title": title,
                "bullet_points": bullet_points
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_image(self, slide_index: int, image_path: str, left: float = 1,
                  top: float = 2, width: Optional[float] = None, height: Optional[float] = None) -> Dict[str, Any]:
        """在幻灯片中添加图片"""
        try:
            if not self.current_presentation:
                return {"success": False, "error": "没有打开的演示文稿"}

            if not Path(image_path).exists():
                return {"success": False, "error": f"图片文件不存在: {image_path}"}

            slides = self.current_presentation.slides
            if slide_index >= len(slides):
                return {"success": False, "error": f"幻灯片索引超出范围: {slide_index}"}

            slide = slides[slide_index]

            # 添加图片
            left_inches = Inches(left)
            top_inches = Inches(top)

            if width and height:
                width_inches = Inches(width)
                height_inches = Inches(height)
                pic = slide.shapes.add_picture(image_path, left_inches, top_inches, width_inches, height_inches)
            else:
                pic = slide.shapes.add_picture(image_path, left_inches, top_inches)

            return {
                "success": True,
                "message": f"成功在幻灯片 {slide_index} 添加图片",
                "image_path": image_path,
                "position": {"left": left, "top": top, "width": width, "height": height}
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_shape(self, slide_index: int, shape_type: str, left: float = 1,
                  top: float = 1, width: float = 2, height: float = 1,
                  fill_color: str = "0066CC") -> Dict[str, Any]:
        """添加形状"""
        try:
            if not self.current_presentation:
                return {"success": False, "error": "没有打开的演示文稿"}

            slides = self.current_presentation.slides
            if slide_index >= len(slides):
                return {"success": False, "error": f"幻灯片索引超出范围: {slide_index}"}

            slide = slides[slide_index]

            # 形状类型映射
            shape_map = {
                "rectangle": MSO_SHAPE.RECTANGLE,
                "oval": MSO_SHAPE.OVAL,
                "triangle": MSO_SHAPE.ISOSCELES_TRIANGLE,
                "diamond": MSO_SHAPE.DIAMOND,
                "pentagon": MSO_SHAPE.REGULAR_PENTAGON,
                "hexagon": MSO_SHAPE.HEXAGON,
                "star": MSO_SHAPE.STAR_5_POINT,
                "arrow": MSO_SHAPE.BLOCK_ARC
            }

            if shape_type.lower() not in shape_map:
                return {"success": False, "error": f"不支持的形状类型: {shape_type}"}

            # 添加形状
            left_inches = Inches(left)
            top_inches = Inches(top)
            width_inches = Inches(width)
            height_inches = Inches(height)

            shape = slide.shapes.add_shape(
                shape_map[shape_type.lower()],
                left_inches, top_inches, width_inches, height_inches
            )

            # 设置填充颜色
            try:
                rgb_color = RGBColor.from_string(fill_color)
                shape.fill.solid()
                shape.fill.fore_color.rgb = rgb_color
            except:
                pass  # 如果颜色格式不正确，使用默认颜色

            return {
                "success": True,
                "message": f"成功在幻灯片 {slide_index} 添加 {shape_type} 形状",
                "shape_type": shape_type,
                "position": {"left": left, "top": top, "width": width, "height": height},
                "fill_color": fill_color
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_presentation_info(self) -> Dict[str, Any]:
        """获取当前演示文稿信息"""
        try:
            if not self.current_presentation:
                return {"success": False, "error": "没有打开的演示文稿"}

            slides_info = []
            for i, slide in enumerate(self.current_presentation.slides):
                slide_info = {
                    "index": i,
                    "shapes_count": len(slide.shapes),
                    "has_title": bool(slide.shapes.title and slide.shapes.title.text),
                    "title": slide.shapes.title.text if slide.shapes.title else ""
                }
                slides_info.append(slide_info)

            return {
                "success": True,
                "file_path": self.current_file_path,
                "slides_count": len(self.current_presentation.slides),
                "slides": slides_info
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def delete_slide(self, slide_index: int) -> Dict[str, Any]:
        """删除指定幻灯片"""
        try:
            if not self.current_presentation:
                return {"success": False, "error": "没有打开的演示文稿"}

            slides = self.current_presentation.slides
            if slide_index >= len(slides):
                return {"success": False, "error": f"幻灯片索引超出范围: {slide_index}"}

            # 删除幻灯片
            slide_id = slides[slide_index].slide_id
            self.current_presentation.part.drop_rel(slide_id)
            del slides._sldIdLst[slide_index]

            return {
                "success": True,
                "message": f"成功删除幻灯片 {slide_index}",
                "remaining_slides": len(self.current_presentation.slides)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}


# 创建PowerPoint编辑器实例
ppt_editor = PowerPointEditor()

# 创建MCP Server
server = Server("powerpoint-editor")

@server.list_tools()
async def handle_list_tools() -> ListToolsResult:
    """列出所有可用的工具"""
    return ListToolsResult(
        tools=[
            Tool(
                name="create_presentation",
                description="创建新的PowerPoint演示文稿",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            ),
            Tool(
                name="open_presentation",
                description="打开现有的PowerPoint演示文稿",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "PowerPoint文件的路径"
                        }
                    },
                    "required": ["file_path"]
                }
            ),
            Tool(
                name="save_presentation",
                description="保存PowerPoint演示文稿",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "保存文件的路径（可选，如果不提供则保存到当前路径）"
                        }
                    },
                    "required": []
                }
            ),
            Tool(
                name="add_slide",
                description="添加新的幻灯片",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "layout_index": {
                            "type": "integer",
                            "description": "幻灯片布局索引（0=标题幻灯片，1=标题和内容，默认为1）",
                            "default": 1
                        }
                    },
                    "required": []
                }
            ),
            Tool(
                name="add_text_box",
                description="在幻灯片中添加文本框",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "slide_index": {
                            "type": "integer",
                            "description": "幻灯片索引（从0开始）"
                        },
                        "text": {
                            "type": "string",
                            "description": "要添加的文本内容"
                        },
                        "left": {
                            "type": "number",
                            "description": "文本框左边距（英寸）",
                            "default": 1
                        },
                        "top": {
                            "type": "number",
                            "description": "文本框上边距（英寸）",
                            "default": 1
                        },
                        "width": {
                            "type": "number",
                            "description": "文本框宽度（英寸）",
                            "default": 8
                        },
                        "height": {
                            "type": "number",
                            "description": "文本框高度（英寸）",
                            "default": 1
                        },
                        "font_size": {
                            "type": "integer",
                            "description": "字体大小",
                            "default": 18
                        },
                        "font_color": {
                            "type": "string",
                            "description": "字体颜色（十六进制，如000000）",
                            "default": "000000"
                        }
                    },
                    "required": ["slide_index", "text"]
                }
            ),
            Tool(
                name="add_title_slide",
                description="添加标题幻灯片",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "title": {
                            "type": "string",
                            "description": "幻灯片标题"
                        },
                        "subtitle": {
                            "type": "string",
                            "description": "幻灯片副标题（可选）",
                            "default": ""
                        }
                    },
                    "required": ["title"]
                }
            ),
            Tool(
                name="add_bullet_points",
                description="添加带项目符号的内容幻灯片",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "slide_index": {
                            "type": "integer",
                            "description": "幻灯片索引（从0开始）"
                        },
                        "title": {
                            "type": "string",
                            "description": "幻灯片标题"
                        },
                        "bullet_points": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "项目符号列表"
                        }
                    },
                    "required": ["slide_index", "title", "bullet_points"]
                }
            ),
            Tool(
                name="add_image",
                description="在幻灯片中添加图片",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "slide_index": {
                            "type": "integer",
                            "description": "幻灯片索引（从0开始）"
                        },
                        "image_path": {
                            "type": "string",
                            "description": "图片文件路径"
                        },
                        "left": {
                            "type": "number",
                            "description": "图片左边距（英寸）",
                            "default": 1
                        },
                        "top": {
                            "type": "number",
                            "description": "图片上边距（英寸）",
                            "default": 2
                        },
                        "width": {
                            "type": "number",
                            "description": "图片宽度（英寸，可选）"
                        },
                        "height": {
                            "type": "number",
                            "description": "图片高度（英寸，可选）"
                        }
                    },
                    "required": ["slide_index", "image_path"]
                }
            ),
            Tool(
                name="add_shape",
                description="在幻灯片中添加形状",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "slide_index": {
                            "type": "integer",
                            "description": "幻灯片索引（从0开始）"
                        },
                        "shape_type": {
                            "type": "string",
                            "description": "形状类型（rectangle, oval, triangle, diamond, pentagon, hexagon, star, arrow）"
                        },
                        "left": {
                            "type": "number",
                            "description": "形状左边距（英寸）",
                            "default": 1
                        },
                        "top": {
                            "type": "number",
                            "description": "形状上边距（英寸）",
                            "default": 1
                        },
                        "width": {
                            "type": "number",
                            "description": "形状宽度（英寸）",
                            "default": 2
                        },
                        "height": {
                            "type": "number",
                            "description": "形状高度（英寸）",
                            "default": 1
                        },
                        "fill_color": {
                            "type": "string",
                            "description": "填充颜色（十六进制，如0066CC）",
                            "default": "0066CC"
                        }
                    },
                    "required": ["slide_index", "shape_type"]
                }
            ),
            Tool(
                name="get_presentation_info",
                description="获取当前演示文稿的信息",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            ),
            Tool(
                name="delete_slide",
                description="删除指定的幻灯片",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "slide_index": {
                            "type": "integer",
                            "description": "要删除的幻灯片索引（从0开始）"
                        }
                    },
                    "required": ["slide_index"]
                }
            )
        ]
    )


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> CallToolResult:
    """处理工具调用"""
    try:
        if name == "create_presentation":
            result = ppt_editor.create_presentation()

        elif name == "open_presentation":
            file_path = arguments.get("file_path")
            result = ppt_editor.open_presentation(file_path)

        elif name == "save_presentation":
            file_path = arguments.get("file_path")
            result = ppt_editor.save_presentation(file_path)

        elif name == "add_slide":
            layout_index = arguments.get("layout_index", 1)
            result = ppt_editor.add_slide(layout_index)

        elif name == "add_text_box":
            slide_index = arguments.get("slide_index")
            text = arguments.get("text")
            left = arguments.get("left", 1)
            top = arguments.get("top", 1)
            width = arguments.get("width", 8)
            height = arguments.get("height", 1)
            font_size = arguments.get("font_size", 18)
            font_color = arguments.get("font_color", "000000")
            result = ppt_editor.add_text_box(slide_index, text, left, top, width, height, font_size, font_color)

        elif name == "add_title_slide":
            title = arguments.get("title")
            subtitle = arguments.get("subtitle", "")
            result = ppt_editor.add_title_slide(title, subtitle)

        elif name == "add_bullet_points":
            slide_index = arguments.get("slide_index")
            title = arguments.get("title")
            bullet_points = arguments.get("bullet_points")
            result = ppt_editor.add_bullet_points(slide_index, title, bullet_points)

        elif name == "add_image":
            slide_index = arguments.get("slide_index")
            image_path = arguments.get("image_path")
            left = arguments.get("left", 1)
            top = arguments.get("top", 2)
            width = arguments.get("width")
            height = arguments.get("height")
            result = ppt_editor.add_image(slide_index, image_path, left, top, width, height)

        elif name == "add_shape":
            slide_index = arguments.get("slide_index")
            shape_type = arguments.get("shape_type")
            left = arguments.get("left", 1)
            top = arguments.get("top", 1)
            width = arguments.get("width", 2)
            height = arguments.get("height", 1)
            fill_color = arguments.get("fill_color", "0066CC")
            result = ppt_editor.add_shape(slide_index, shape_type, left, top, width, height, fill_color)

        elif name == "get_presentation_info":
            result = ppt_editor.get_presentation_info()

        elif name == "delete_slide":
            slide_index = arguments.get("slide_index")
            result = ppt_editor.delete_slide(slide_index)

        else:
            result = {"success": False, "error": f"未知的工具: {name}"}

        # 返回结果
        if result.get("success"):
            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
            )
        else:
            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))],
                isError=True
            )

    except Exception as e:
        logger.error(f"工具调用错误: {e}")
        error_result = {"success": False, "error": str(e)}
        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps(error_result, ensure_ascii=False, indent=2))],
            isError=True
        )


async def main():
    """主函数"""
    # 使用stdio运行服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="powerpoint-editor",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities={}
                )
            )
        )


if __name__ == "__main__":
    asyncio.run(main())